import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:frontend/models/document.dart';
import '../models/document.dart' as doc_model;
import '../services/api_service.dart';

class DocumentEditorScreen extends StatefulWidget {
  final doc_model.Document? document;

  const DocumentEditorScreen({Key? key, this.document}) : super(key: key);

  @override
  State<DocumentEditorScreen> createState() => _DocumentEditorScreenState();
}

class _DocumentEditorScreenState extends State<DocumentEditorScreen> {
  final ApiService _apiService = ApiService();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _authorController = TextEditingController();
  late QuillController _quillController;
  
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeEditor();
  }

  void _initializeEditor() {
    if (widget.document != null) {
      // Editing existing document
      _titleController.text = widget.document!.title;
      _authorController.text = widget.document!.author;
      
      // Try to parse content as Quill delta, fallback to plain text
      try {
        final delta = Delta.fromJson(jsonDecode(widget.document!.content));
        _quillController = QuillController(
          document: quill.Document.fromDelta(delta),
          selection: const TextSelection.collapsed(offset: 0),
        );
      } catch (e) {
        // If content is not valid JSON/Delta, treat as plain text
        _quillController = QuillController.basic();
        _quillController.document.insert(0, widget.document!.content);
      }
    } else {
      // Creating new document
      _titleController.text = 'Untitled Document';
      _authorController.text = 'Anonymous';
      _quillController = QuillController.basic();
    }

    // Listen for changes
    _titleController.addListener(_onContentChanged);
    _authorController.addListener(_onContentChanged);
    _quillController.addListener(_onContentChanged);
  }

  void _onContentChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  Future<void> _saveDocument() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a document title')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final content = jsonEncode(_quillController.document.toDelta().toJson());
      
      if (widget.document != null) {
        // Update existing document
        final request = doc_model.UpdateDocumentRequest(
          title: _titleController.text.trim(),
          content: content,
        );
        await _apiService.updateDocument(widget.document!.id, request);
      } else {
        // Create new document
        final request = doc_model.CreateDocumentRequest(
          title: _titleController.text.trim(),
          content: content,
          author: _authorController.text.trim(),
        );
        await _apiService.createDocument(request);
      }

      setState(() {
        _hasUnsavedChanges = false;
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Document saved successfully')),
      );

      Navigator.of(context).pop(true); // Return true to indicate success
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save document: $e')),
      );
    }
  }

  Future<bool> _onWillPop() async {
    if (!_hasUnsavedChanges) return true;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsaved Changes'),
        content: const Text('You have unsaved changes. Do you want to save before leaving?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Discard'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true) {
      await _saveDocument();
      return !_hasUnsavedChanges; // Only leave if save was successful
    }

    return result == false; // Leave if user chose to discard
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          final shouldPop = await _onWillPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.document != null ? 'Edit Document' : 'New Document'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          actions: [
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              )
            else
              IconButton(
                onPressed: _saveDocument,
                icon: const Icon(Icons.save),
                tooltip: 'Save Document',
              ),
          ],
        ),
        body: Column(
          children: [
            // Document metadata
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[50],
              child: Column(
                children: [
                  TextField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Document Title',
                      border: OutlineInputBorder(),
                    ),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  if (widget.document == null)
                    TextField(
                      controller: _authorController,
                      decoration: const InputDecoration(
                        labelText: 'Author',
                        border: OutlineInputBorder(),
                      ),
                    ),
                ],
              ),
            ),
            // Quill toolbar
            QuillToolbar.simple(
              configurations: QuillSimpleToolbarConfigurations(
                controller: _quillController,
                sharedConfigurations: const QuillSharedConfigurations(
                  locale: Locale('en'),
                ),
              ),
            ),
            // Editor
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                child: QuillEditor.basic(
                  configurations: QuillEditorConfigurations(
                    controller: _quillController,
                    placeholder: 'Start writing your document...',
                    sharedConfigurations: const QuillSharedConfigurations(
                      locale: Locale('en'),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _authorController.dispose();
    _quillController.dispose();
    super.dispose();
  }
}


