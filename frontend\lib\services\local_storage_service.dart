import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:uuid/uuid.dart';
import '../models/document.dart' as doc_model;

class LocalStorageService {
  static Database? _database;
  static const String _tableName = 'documents';
  final Uuid _uuid = const Uuid();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'documents.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createTable,
    );
  }

  Future<void> _createTable(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        author TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  // Get all documents
  Future<List<doc_model.Document>> getDocuments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      orderBy: 'updated_at DESC',
    );

    return List.generate(maps.length, (i) {
      return doc_model.Document.fromJson(maps[i]);
    });
  }

  // Get a specific document
  Future<doc_model.Document?> getDocument(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return doc_model.Document.fromJson(maps.first);
    }
    return null;
  }

  // Create a new document
  Future<doc_model.Document> createDocument(doc_model.CreateDocumentRequest request) async {
    final db = await database;
    final now = DateTime.now();
    final id = _uuid.v4();

    final document = doc_model.Document(
      id: id,
      title: request.title,
      content: request.content,
      author: request.author,
      createdAt: now,
      updatedAt: now,
    );

    await db.insert(_tableName, document.toJson());
    return document;
  }

  // Update an existing document
  Future<doc_model.Document> updateDocument(String id, doc_model.UpdateDocumentRequest request) async {
    final db = await database;
    final existing = await getDocument(id);
    
    if (existing == null) {
      throw Exception('Document not found');
    }

    final updated = existing.copyWith(
      title: request.title,
      content: request.content,
      updatedAt: DateTime.now(),
    );

    await db.update(
      _tableName,
      updated.toJson(),
      where: 'id = ?',
      whereArgs: [id],
    );

    return updated;
  }

  // Delete a document
  Future<void> deleteDocument(String id) async {
    final db = await database;
    final result = await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (result == 0) {
      throw Exception('Document not found');
    }
  }

  // Close the database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
