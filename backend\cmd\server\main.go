package main

import (
	"fmt"
	"log"
	"net/http"

	"doc-suite-backend/internal/handlers"
	"doc-suite-backend/internal/middleware"
	"doc-suite-backend/internal/storage"

	"github.com/gorilla/mux"
)

func main() {
	// Initialize storage
	fileStorage := storage.NewFileStorage("./data")

	// Initialize handlers
	documentHandler := handlers.NewDocumentHandler(fileStorage)

	// Setup router
	router := mux.NewRouter()

	// API routes
	api := router.PathPrefix("/api").Subrouter()
	api.HandleFunc("/documents", documentHandler.GetDocuments).Methods("GET")
	api.HandleFunc("/documents/{id}", documentHandler.GetDocument).Methods("GET")
	api.HandleFunc("/documents", documentHandler.CreateDocument).Methods("POST")
	api.HandleFunc("/documents/{id}", documentHandler.UpdateDocument).Methods("PUT")
	api.HandleFunc("/documents/{id}", documentHandler.DeleteDocument).Methods("DELETE")

	// Setup CORS
	corsHandler := middleware.SetupCORS()
	handler := corsHandler.Handler(router)

	// Start server
	port := "8080"
	fmt.Printf("Server starting on port %s...\n", port)
	fmt.Printf("API available at: http://localhost:%s/api\n", port)
	
	log.Fatal(http.ListenAndServe(":"+port, handler))
}
