package models

import (
	"time"
)

// Document represents a document in the system
type Document struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Author      string    `json:"author"`
}

// CreateDocumentRequest represents the request payload for creating a document
type CreateDocumentRequest struct {
	Title   string `json:"title"`
	Content string `json:"content"`
	Author  string `json:"author"`
}

// UpdateDocumentRequest represents the request payload for updating a document
type UpdateDocumentRequest struct {
	Title   string `json:"title"`
	Content string `json:"content"`
}
