﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{DB0E270F-1E93-3C7E-BE6C-529CE27061B5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{C4BB96BA-996F-37B4-96BE-F635B66473B4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
