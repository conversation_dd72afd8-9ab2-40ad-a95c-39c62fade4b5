@echo off
echo Building Document Suite for Microsoft Store...
echo.

echo Step 1: Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo Step 2: Building Windows release...
flutter build windows --release

echo.
echo Step 3: Preparing for Microsoft Store packaging...
echo.
echo Your app has been built successfully!
echo.
echo Next steps for Microsoft Store submission:
echo.
echo 1. Install Visual Studio 2022 with "Windows Application Packaging Project" workload
echo 2. Create a new "Windows Application Packaging Project" in Visual Studio
echo 3. Add your built app as a reference:
echo    - Right-click "Applications" in Solution Explorer
echo    - Add Reference -> Browse
echo    - Select: build\windows\x64\runner\Release\document_suite.exe
echo 4. Configure Package.appxmanifest with your app details
echo 5. Build the MSIX package
echo 6. Upload to Microsoft Partner Center
echo.
echo Built files location: build\windows\x64\runner\Release\
echo.
echo For detailed instructions, see: MICROSOFT_STORE_GUIDE.md
echo.
pause
