package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"doc-suite-backend/internal/models"
	"doc-suite-backend/internal/storage"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
)

// DocumentHandler handles HTTP requests for documents
type DocumentHandler struct {
	storage *storage.FileStorage
}

// NewDocumentHandler creates a new document handler
func NewDocumentHandler(storage *storage.FileStorage) *DocumentHandler {
	return &DocumentHandler{storage: storage}
}

// GetDocuments handles GET /api/documents
func (h *DocumentHandler) GetDocuments(w http.ResponseWriter, r *http.Request) {
	documents, err := h.storage.GetAll()
	if err != nil {
		http.Error(w, "Failed to retrieve documents", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(documents)
}

// GetDocument handles GET /api/documents/{id}
func (h *DocumentHandler) GetDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	document, err := h.storage.GetByID(id)
	if err != nil {
		http.Error(w, "Document not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(document)
}

// CreateDocument handles POST /api/documents
func (h *DocumentHandler) CreateDocument(w http.ResponseWriter, r *http.Request) {
	var req models.CreateDocumentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Generate UUID for the document
	id := uuid.New().String()
	now := time.Now()

	document := &models.Document{
		ID:        id,
		Title:     req.Title,
		Content:   req.Content,
		Author:    req.Author,
		CreatedAt: now,
		UpdatedAt: now,
	}

	if err := h.storage.Create(document); err != nil {
		http.Error(w, "Failed to create document", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(document)
}

// UpdateDocument handles PUT /api/documents/{id}
func (h *DocumentHandler) UpdateDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	// Check if document exists
	document, err := h.storage.GetByID(id)
	if err != nil {
		http.Error(w, "Document not found", http.StatusNotFound)
		return
	}

	var req models.UpdateDocumentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update document fields
	document.Title = req.Title
	document.Content = req.Content
	document.UpdatedAt = time.Now()

	if err := h.storage.Update(document); err != nil {
		http.Error(w, "Failed to update document", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(document)
}

// DeleteDocument handles DELETE /api/documents/{id}
func (h *DocumentHandler) DeleteDocument(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	if err := h.storage.Delete(id); err != nil {
		http.Error(w, "Failed to delete document", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}
