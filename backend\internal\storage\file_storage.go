package storage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"doc-suite-backend/internal/models"
)

// FileStorage implements document storage using the file system
type FileStorage struct {
	dataDir string
}

// NewFileStorage creates a new file storage instance
func NewFileStorage(dataDir string) *FileStorage {
	// Create data directory if it doesn't exist
	os.MkdirAll(dataDir, 0755)
	return &FileStorage{dataDir: dataDir}
}

// GetAll returns all documents
func (fs *FileStorage) GetAll() ([]models.Document, error) {
	files, err := os.ReadDir(fs.dataDir)
	if err != nil {
		return nil, err
	}

	var documents []models.Document
	for _, file := range files {
		if filepath.Ext(file.Name()) == ".json" {
			doc, err := fs.loadDocument(file.Name())
			if err != nil {
				continue // Skip corrupted files
			}
			documents = append(documents, doc)
		}
	}

	return documents, nil
}

// GetByID returns a document by its ID
func (fs *FileStorage) GetByID(id string) (*models.Document, error) {
	filename := fmt.Sprintf("%s.json", id)
	doc, err := fs.loadDocument(filename)
	if err != nil {
		return nil, err
	}
	return &doc, nil
}

// Create creates a new document
func (fs *FileStorage) Create(doc *models.Document) error {
	filename := fmt.Sprintf("%s.json", doc.ID)
	return fs.saveDocument(filename, doc)
}

// Update updates an existing document
func (fs *FileStorage) Update(doc *models.Document) error {
	filename := fmt.Sprintf("%s.json", doc.ID)
	return fs.saveDocument(filename, doc)
}

// Delete deletes a document by its ID
func (fs *FileStorage) Delete(id string) error {
	filename := fmt.Sprintf("%s.json", id)
	filePath := filepath.Join(fs.dataDir, filename)
	return os.Remove(filePath)
}

// loadDocument loads a document from a file
func (fs *FileStorage) loadDocument(filename string) (models.Document, error) {
	var doc models.Document
	filePath := filepath.Join(fs.dataDir, filename)

	data, err := os.ReadFile(filePath)
	if err != nil {
		return doc, err
	}

	err = json.Unmarshal(data, &doc)
	return doc, err
}

// saveDocument saves a document to a file
func (fs *FileStorage) saveDocument(filename string, doc *models.Document) error {
	filePath := filepath.Join(fs.dataDir, filename)

	data, err := json.MarshalIndent(doc, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(filePath, data, 0644)
}
