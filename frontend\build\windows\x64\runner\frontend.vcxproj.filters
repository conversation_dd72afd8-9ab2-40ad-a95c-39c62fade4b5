﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\runner\flutter_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\runner\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\runner\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\runner\win32_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\generated_plugin_registrant.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\runner\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\runner\Runner.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{C4BB96BA-996F-37B4-96BE-F635B66473B4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
