# Microsoft Store Submission Guide - Document Suite

## 🏪 Complete Step-by-Step Guide

### Prerequisites
1. **Microsoft Developer Account** ($19 one-time fee)
   - Sign up at: https://developer.microsoft.com/en-us/microsoft-store/register/
2. **Visual Studio 2022** (Community Edition is free)
   - Download from: https://visualstudio.microsoft.com/downloads/
   - Install with "Windows Application Packaging Project" workload

### Step 1: Build Your App for Store
```bash
cd frontend
package_for_store.bat
```

### Step 2: Create MSIX Package with Visual Studio

#### 2.1 Create Packaging Project
1. Open Visual Studio 2022
2. Create New Project → "Windows Application Packaging Project"
3. Name: "DocumentSuitePackage"
4. Location: Choose a folder (e.g., Desktop)

#### 2.2 Add Your App
1. Right-click "Applications" in Solution Explorer
2. Add → Reference → Browse
3. Navigate to: `build\windows\x64\runner\Release\document_suite.exe`
4. Select the exe file and click OK

#### 2.3 Configure Package Manifest
1. Double-click "Package.appxmanifest" in Solution Explorer
2. **Application Tab**:
   - Display Name: `Document Suite`
   - Description: `A powerful document editor with rich text formatting`
   - Entry Point: `document_suite.exe`

3. **Visual Assets Tab**:
   - Upload app icons (required sizes: 44x44, 50x50, 150x150, 310x150, 310x310)
   - Use online tools like https://www.appicon.co/ to generate all sizes

4. **Capabilities Tab**:
   - Check only: "Internet (Client & Server)" (even though we don't use it)
   - Uncheck everything else for minimal permissions

5. **Declarations Tab**:
   - No additional declarations needed

#### 2.4 Build MSIX Package
1. Right-click the packaging project → "Publish" → "Create App Packages"
2. Choose "Microsoft Store using a new app name"
3. Sign in with your Microsoft Developer account
4. Reserve app name: "Document Suite" (or similar)
5. Follow the wizard to create the package

### Step 3: Submit to Microsoft Store

#### 3.1 Access Partner Center
1. Go to: https://partner.microsoft.com/en-us/dashboard/commercial-marketplace/overview
2. Sign in with your developer account
3. Click "New Product" → "MSIX or PWA app"

#### 3.2 App Identity
- **Product Name**: Document Suite
- **Product Type**: App
- **Device Family**: Desktop

#### 3.3 Pricing and Availability
- **Pricing**: Free (recommended) or set a price
- **Markets**: Select all markets or specific regions
- **Visibility**: Public

#### 3.4 Properties
- **Category**: Productivity
- **Subcategory**: Office
- **Privacy Policy**: Create a simple one (see template below)
- **Age Rating**: Everyone

#### 3.5 Store Listings
- **Description**: 
```
Create, edit, and organize your documents with Document Suite - a powerful offline document editor with rich text formatting.

KEY FEATURES:
• Rich text editing with bold, italic, lists, and more
• Completely offline - no internet required
• Local storage for maximum privacy
• Clean, modern interface
• Instant startup - no loading screens
• No subscriptions or in-app purchases

Perfect for students, professionals, and anyone who needs a reliable document editor that works anywhere, anytime.
```

- **Keywords**: document, editor, text, writing, productivity, offline, rich text
- **Screenshots**: Upload 3-10 screenshots showing:
  - Main document list
  - Document editor with formatting
  - Rich text toolbar
  - Empty state ("Nothing to see here")

#### 3.6 Packages
1. Upload the MSIX package created in Step 2
2. The system will validate your package
3. Fix any validation errors if they appear

#### 3.7 Submit for Certification
1. Review all sections
2. Click "Submit to the Store"
3. Certification typically takes 24-72 hours

### Step 4: Post-Submission

#### 4.1 Monitor Status
- Check Partner Center for certification status
- Respond to any feedback from Microsoft

#### 4.2 Once Approved
- Your app will be live in Microsoft Store
- Users can search for "Document Suite" and install
- You'll receive analytics and user feedback

## 📋 Required Assets

### App Icons (create these sizes)
- 44x44 pixels
- 50x50 pixels  
- 150x150 pixels
- 310x150 pixels (wide tile)
- 310x310 pixels (large tile)

### Screenshots (1366x768 or higher)
- Document list view
- Document editor interface
- Rich text formatting in action
- Empty state screen

## 🔒 Privacy Policy Template

Create a simple privacy policy at https://www.privacypolicytemplate.net/ or use this template:

```
Privacy Policy for Document Suite

Last updated: [Date]

Document Suite ("we", "our", or "us") operates the Document Suite application.

INFORMATION COLLECTION
Document Suite does not collect, store, or transmit any personal information. All documents and data remain locally on your device.

DATA STORAGE
All documents are stored locally on your device using SQLite database. No data is sent to external servers.

CONTACT US
If you have questions about this Privacy Policy, contact us at: [<EMAIL>]
```

## ✅ Checklist Before Submission

- [ ] App builds successfully with `package_for_store.bat`
- [ ] MSIX package created with Visual Studio
- [ ] All required app icons prepared
- [ ] Screenshots taken and optimized
- [ ] Privacy policy created and hosted
- [ ] Microsoft Developer account active
- [ ] App description written
- [ ] Keywords selected
- [ ] Pricing set (Free recommended)

## 🎯 Tips for Approval

1. **Test thoroughly** - Make sure all features work
2. **Follow guidelines** - Read Microsoft Store policies
3. **Quality screenshots** - Show your app's best features
4. **Clear description** - Explain what your app does
5. **Minimal permissions** - Only request what you need
6. **Age rating** - Choose "Everyone" for broad appeal

## 🚀 Expected Timeline

- **Package creation**: 30 minutes
- **Store listing setup**: 1-2 hours  
- **Certification**: 24-72 hours
- **Live in store**: Immediately after approval

Your Document Suite app is perfectly suited for the Microsoft Store with its offline functionality, local storage, and clean interface!
