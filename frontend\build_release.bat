@echo off
echo Building Document Suite for Release...
echo.

echo Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo Building Android APK...
flutter build apk --release

echo.
echo Building Android App Bundle (for Play Store)...
flutter build appbundle --release

echo.
echo Building Windows executable...
flutter build windows --release

echo.
echo Build complete!
echo.
echo Files created:
echo - Android APK: build\app\outputs\flutter-apk\app-release.apk
echo - Android Bundle: build\app\outputs\bundle\release\app-release.aab
echo - Windows: build\windows\x64\runner\Release\
echo.
echo Ready for distribution!
pause
