^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\DOC SUITE\FRONTEND\BUILD\WINDOWS\X64\CMAKEFILES\F5A7B570124F875324065F752A4D77B5\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Documents/augment-projects/doc suite/frontend/windows" "-BC:/Users/<USER>/Documents/augment-projects/doc suite/frontend/build/windows/x64" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/Documents/augment-projects/doc suite/frontend/build/windows/x64/frontend.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
