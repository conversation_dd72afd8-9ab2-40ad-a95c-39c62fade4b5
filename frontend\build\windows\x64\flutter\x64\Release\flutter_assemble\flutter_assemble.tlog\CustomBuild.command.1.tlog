^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\DOC SUITE\FRONTEND\BUILD\WINDOWS\X64\CMAKEFILES\828FC90C3E41C0ECDE66AF0EE2E07BC2\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\Users\<USER>\flutter "PROJECT_DIR=C:\Users\<USER>\Documents\augment-projects\doc suite\frontend" FLUTTER_ROOT=C:\Users\<USER>\flutter "FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\windows\flutter\ephemeral" "PROJECT_DIR=C:\Users\<USER>\Documents\augment-projects\doc suite\frontend" FLUTTER_TARGET=lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true "PACKAGE_CONFIG=C:\Users\<USER>\Documents\augment-projects\doc suite\frontend\.dart_tool\package_config.json" C:/Users/<USER>/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\DOC SUITE\FRONTEND\BUILD\WINDOWS\X64\CMAKEFILES\DD3DF73BBF0D0EC04EE0C99D2EF488A2\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\DOC SUITE\FRONTEND\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/Documents/augment-projects/doc suite/frontend/windows" "-BC:/Users/<USER>/Documents/augment-projects/doc suite/frontend/build/windows/x64" --check-stamp-file "C:/Users/<USER>/Documents/augment-projects/doc suite/frontend/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
