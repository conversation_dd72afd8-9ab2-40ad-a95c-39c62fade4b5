#!/bin/bash

echo "Building Document Suite for Release..."
echo

echo "Cleaning previous builds..."
flutter clean
flutter pub get

echo
echo "Building Android APK..."
flutter build apk --release

echo
echo "Building Android App Bundle (for Play Store)..."
flutter build appbundle --release

echo
echo "Building iOS (if on macOS)..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    flutter build ios --release
    echo "iOS build complete: build/ios/iphoneos/Runner.app"
else
    echo "Skipping iOS build (not on macOS)"
fi

echo
echo "Building Linux (if on Linux)..."
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    flutter build linux --release
    echo "Linux build complete: build/linux/x64/release/bundle/"
else
    echo "Skipping Linux build (not on Linux)"
fi

echo
echo "Build complete!"
echo
echo "Files created:"
echo "- Android APK: build/app/outputs/flutter-apk/app-release.apk"
echo "- Android Bundle: build/app/outputs/bundle/release/app-release.aab"
echo
echo "Ready for distribution!"
