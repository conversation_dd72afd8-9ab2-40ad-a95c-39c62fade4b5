import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/document.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8080/api';

  // Get all documents
  Future<List<Document>> getDocuments() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/documents'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        return jsonList.map((json) => Document.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load documents: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to load documents: $e');
    }
  }

  // Get a specific document
  Future<Document> getDocument(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/documents/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return Document.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load document: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to load document: $e');
    }
  }

  // Create a new document
  Future<Document> createDocument(CreateDocumentRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/documents'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 201) {
        return Document.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to create document: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to create document: $e');
    }
  }

  // Update an existing document
  Future<Document> updateDocument(String id, UpdateDocumentRequest request) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/documents/$id'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        return Document.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to update document: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to update document: $e');
    }
  }

  // Delete a document
  Future<void> deleteDocument(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/documents/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode != 204) {
        throw Exception('Failed to delete document: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to delete document: $e');
    }
  }
}
